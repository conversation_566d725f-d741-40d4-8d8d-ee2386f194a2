#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集清理脚本 - 平衡版本
只删除明显不相关或严重不完整的数据
"""

import json
import re

def is_obviously_problematic(doc_token, doc_label):
    """检查是否是明显有问题的数据"""
    
    # 1. 检查是否是空的或过短的无意义内容
    if len(doc_token.strip()) < 10:
        return True
    
    # 2. 检查是否是纯指令性文字（没有具体数学问题）
    pure_instruction_patterns = [
        r'^画一画$',
        r'^填一填$', 
        r'^算一算$',
        r'^想一想$',
        r'^说一说$',
        r'^圈一圈$',
        r'^连一连$',
    ]
    
    for pattern in pure_instruction_patterns:
        if re.search(pattern, doc_token.strip()):
            return True
    
    # 3. 检查是否是明显不完整的选择题
    # 如果提到选择但没有选项，且题目很短
    if len(doc_token) < 50:
        if ('选择' in doc_token or '画√' in doc_token or '画×' in doc_token):
            # 检查是否有选项标识
            has_options = bool(re.search(r'[ABCD①②③④]', doc_token))
            if not has_options:
                return True
    
    # 4. 检查是否是明显缺少关键信息的题目
    problematic_patterns = [
        r'^在.*括号里画√$',  # 只说在括号里画√但没有给出具体内容
        r'^在.*下面画√$',    # 类似的不完整指令
        r'^选择正确答案$',    # 没有给出选项的选择题
    ]
    
    for pattern in problematic_patterns:
        if re.search(pattern, doc_token.strip()):
            return True
    
    # 5. 检查是否是标签与内容完全不匹配的情况
    # 如果标签都是关于具体数学概念，但题目中没有任何数学元素
    if len(doc_token) < 30:
        # 检查是否包含基本的数学元素
        math_indicators = [
            r'\d+',  # 数字
            r'[=+\-×÷<>]',  # 运算符和比较符
            r'(厘米|米|千米|分米|毫米|cm|m|km|dm|mm)',  # 长度单位
            r'(克|千克|吨|g|kg|t)',  # 重量单位
            r'(元|角|分|￥)',  # 货币单位
            r'(分钟|小时|天|月|年|秒)',  # 时间单位
            r'(平方|立方|²|³)',  # 面积体积
            r'(三角形|正方形|长方形|圆形|梯形|平行四边形)',  # 几何图形
            r'(角|度|°)',  # 角度
            r'(分数|小数|整数|奇数|偶数)',  # 数的类型
        ]
        
        has_math_content = any(re.search(pattern, doc_token) for pattern in math_indicators)
        
        # 如果没有数学内容，但标签很具体，可能是有问题的数据
        if not has_math_content:
            specific_math_labels = [
                label for label in doc_label 
                if not label.startswith('小学数学新知识树') 
                and not label.startswith('补充知识点')
                and any(keyword in label for keyword in ['运算', '计算', '几何', '图形', '数的', '量'])
            ]
            if len(specific_math_labels) > 2:
                return True
    
    return False

def clean_dataset_balanced(input_file, output_file):
    """平衡地清理数据集"""
    print(f"正在读取文件: {input_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"原始数据条目数: {len(data)}")
    
    # 过滤有问题的数据
    clean_data = []
    problematic_count = 0
    
    for i, item in enumerate(data):
        if i % 1000 == 0:
            print(f"处理进度: {i}/{len(data)}")
        
        if not is_obviously_problematic(item['doc_token'], item['doc_label']):
            clean_data.append(item)
        else:
            problematic_count += 1
            # 打印一些被删除的样本用于检查
            if problematic_count <= 20:
                print(f"\n删除的样本 {problematic_count}:")
                print(f"Token: {item['doc_token'][:100]}...")
                print(f"Labels: {item['doc_label'][:3]}...")
    
    print(f"\n清理完成!")
    print(f"删除的有问题条目数: {problematic_count}")
    print(f"保留的条目数: {len(clean_data)}")
    print(f"删除比例: {problematic_count/len(data)*100:.2f}%")
    
    # 保存清理后的数据
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(clean_data, f, ensure_ascii=False, indent=2)
    
    print(f"清理后的数据已保存到: {output_file}")

if __name__ == "__main__":
    # 设置参数
    input_file = "math1_test_origin.json"
    output_file = "math1_test_cleaned_balanced.json"
    
    # 执行清理
    clean_dataset_balanced(input_file, output_file)
