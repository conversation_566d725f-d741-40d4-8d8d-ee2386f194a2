#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集清理脚本
删除doc_label和doc_token无关的数据条目
"""

import json
import re

def simple_chinese_tokenize(text: str) -> set:
    """简单的中文分词，基于常见词汇"""
    # 数学相关关键词
    math_keywords = [
        '计算', '算式', '乘法', '除法', '加法', '减法', '数字', '倍数', '因数', '分数', '小数', '整数',
        '几何', '图形', '面积', '周长', '体积', '时间', '长度', '重量', '角度', '直线', '曲线',
        '正方形', '长方形', '三角形', '圆形', '立体', '平面', '点', '线', '面',
        '大于', '小于', '等于', '比较', '排序', '顺序', '相邻', '中间',
        '应用题', '解决', '问题', '方法', '步骤', '过程', '结果',
        '认识', '理解', '掌握', '运用', '练习', '巩固'
    ]

    # 学科分类关键词
    subject_keywords = [
        '数与代数', '图形与几何', '统计与概率', '综合与实践',
        '数的认识', '数的运算', '常见的量', '平面图形', '立体图形', '位置与方向'
    ]

    # 年级相关
    grade_keywords = ['小学', '一年级', '二年级', '三年级', '四年级', '五年级', '六年级']

    all_keywords = math_keywords + subject_keywords + grade_keywords

    found_keywords = set()
    for keyword in all_keywords:
        if keyword in text:
            found_keywords.add(keyword)

    return found_keywords

def extract_keywords_from_token(doc_token: str) -> set:
    """从doc_token中提取关键词"""
    return simple_chinese_tokenize(doc_token)

def extract_keywords_from_labels(doc_label) -> set:
    """从doc_label中提取关键词"""
    all_keywords = set()

    for label in doc_label:
        # 跳过通用标签
        if label in ['小学数学新知识树', '数学竞赛'] or label.startswith('补充知识点'):
            continue

        keywords = simple_chinese_tokenize(label)
        all_keywords.update(keywords)

    return all_keywords

def calculate_relevance_score(doc_token: str, doc_label: List[str]) -> float:
    """计算doc_token和doc_label的相关性分数"""
    token_keywords = extract_keywords_from_token(doc_token)
    label_keywords = extract_keywords_from_labels(doc_label)
    
    if not token_keywords or not label_keywords:
        return 0.0
    
    # 计算交集
    intersection = token_keywords.intersection(label_keywords)
    
    # 计算相关性分数 (Jaccard相似度)
    union = token_keywords.union(label_keywords)
    if not union:
        return 0.0
    
    jaccard_score = len(intersection) / len(union)
    
    # 额外检查：如果token中包含数学相关的关键词，给予加分
    math_keywords = {'计算', '算式', '乘法', '除法', '加法', '减法', '数字', '倍数', '因数', '分数', '小数', '整数', '几何', '图形', '面积', '周长', '体积', '时间', '长度', '重量'}
    math_bonus = len(token_keywords.intersection(math_keywords)) * 0.1
    
    return min(jaccard_score + math_bonus, 1.0)

def is_relevant(item: Dict[str, Any], threshold: float = 0.1) -> bool:
    """判断一个数据项是否相关"""
    doc_token = item.get('doc_token', '')
    doc_label = item.get('doc_label', [])
    
    if not doc_token or not doc_label:
        return False
    
    # 计算相关性分数
    score = calculate_relevance_score(doc_token, doc_label)
    
    return score >= threshold

def clean_dataset(input_file: str, output_file: str, threshold: float = 0.1):
    """清理数据集"""
    print(f"正在读取文件: {input_file}")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"原始数据条目数: {len(data)}")
    
    # 过滤相关数据
    relevant_data = []
    irrelevant_count = 0
    
    for i, item in enumerate(data):
        if i % 1000 == 0:
            print(f"处理进度: {i}/{len(data)}")
        
        if is_relevant(item, threshold):
            relevant_data.append(item)
        else:
            irrelevant_count += 1
            # 打印一些被删除的样本用于检查
            if irrelevant_count <= 10:
                print(f"\n删除的样本 {irrelevant_count}:")
                print(f"Token: {item['doc_token'][:100]}...")
                print(f"Labels: {item['doc_label'][:5]}...")
    
    print(f"\n清理完成!")
    print(f"删除的不相关条目数: {irrelevant_count}")
    print(f"保留的相关条目数: {len(relevant_data)}")
    print(f"删除比例: {irrelevant_count/len(data)*100:.2f}%")
    
    # 保存清理后的数据
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(relevant_data, f, ensure_ascii=False, indent=2)
    
    print(f"清理后的数据已保存到: {output_file}")

if __name__ == "__main__":
    # 设置参数
    input_file = "math1_test_origin.json"
    output_file = "math1_test_cleaned.json"
    threshold = 0.1  # 相关性阈值，可以调整
    
    # 执行清理
    clean_dataset(input_file, output_file, threshold)
